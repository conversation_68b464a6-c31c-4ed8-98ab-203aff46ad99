
"use client";

import { useState, useEffect } from "react";
import { whiteLoadingSvg } from "../svg";
import Particles from "~/components/ui/Particles";
import { toggleFullscreen } from "~/utils/full-screen";
import Link from "next/link";
import {
  FacebookShareButton, FacebookIcon,
  TwitterShareButton, TwitterIcon,
  RedditShareButton, RedditIcon,
  WhatsappShareButton, WhatsappIcon,
  TelegramShareButton, TelegramIcon,
  LineShareButton, LineIcon
} from 'react-share';
import { LandingComponentStyles, RecommendedGame } from "./types";
import RecommendedGameCard from "~/components/common/RecommendedGameCard";
// import { ShareSocial } from 'react-share-social';

interface IframeProps {
  landingInfo: {
    title?: string;
    description?: string;
    h1?: string;
    src?: string;
    iframeSrc?: string;
    width?: string | number;
    height?: string | number;
    allowFullScreen?: boolean;
    aspectRatio?: "16:9" | "4:3" | "1:1" | "3:4";
    buttonText?: string;
    buttonLink?: string;
  };
  styles?: LandingComponentStyles;
  className?: string;
  recommendedGames?: RecommendedGame[]; // 新增推荐游戏支持
  showRecommendedGames?: boolean; // 控制是否显示推荐游戏
}

export default function IframeCard({
  landingInfo,
  styles,
  className,
  recommendedGames = [],
  showRecommendedGames = true
}: IframeProps) {
  const [loading, setLoading] = useState(true);
  const [isFullScreen, setIsFullScreen] = useState(false);
  const [isClient, setIsClient] = useState(false);
  const [windowWidth, setWindowWidth] = useState(0);
  const { 
    src, 
    iframeSrc = "",
    title = "", 
    description = "", 
    width = "100%", 
    height = "600px", 
    allowFullScreen = true,
    buttonText = "Play Now",
    buttonLink = "#"
  } = landingInfo;

  // 使用src或iframeSrc作为iframe源
  const iframeSource = src || iframeSrc;

  const h1Text = landingInfo.h1 || "";
  const dashIndex = h1Text.indexOf("-");
  let mainTitle = h1Text;
  let subTitle = "";
  if (dashIndex !== -1) {
    mainTitle = h1Text.slice(0, dashIndex);
    subTitle = h1Text.slice(dashIndex); // 不做 trim，保留原始空格
  }

  useEffect(() => {
    const handleFullscreenChange = () => {
      const isFullscreenNow = !!(document.fullscreenElement || (document as any).webkitFullscreenElement || (document as any).mozFullScreenElement || (document as any).msFullscreenElement);
      setIsFullScreen(isFullscreenNow);
    };

    document.addEventListener("fullscreenchange", handleFullscreenChange);
    document.addEventListener("webkitfullscreenchange", handleFullscreenChange);
    document.addEventListener("mozfullscreenchange", handleFullscreenChange);
    document.addEventListener("MSFullscreenChange", handleFullscreenChange);

    return () => {
      document.removeEventListener("fullscreenchange", handleFullscreenChange);
      document.removeEventListener("webkitfullscreenchange", handleFullscreenChange);
      document.removeEventListener("mozfullscreenchange", handleFullscreenChange);
      document.removeEventListener("MSFullscreenChange", handleFullscreenChange);
    };
  }, []);

  useEffect(() => {
    setIsClient(true);
  }, []);

  // 调试信息
  console.log('🎮 IframeCard Debug:', {
    showRecommendedGames,
    recommendedGamesLength: recommendedGames.length,
    hasGamesForLeft: recommendedGames.length > 0,
    hasGamesForRight: recommendedGames.length > 6,
    recommendedGames: recommendedGames.slice(0, 3), // 显示前3个
  });

  // 强制显示侧边栏的测试数据 - 使用真实游戏数据
  const forceTestGames: RecommendedGame[] = [
    {
      title: 'Sprunka2',
      image: '/icon/favicon.ico',
      cover_img_url: 'https://tinyfun.io/images/sprunka2-sprunka-sprunka.webp',
      link: '/sprunka2-sprunka-sprunka',
      description: 'Create amazing music with Sprunka characters',
      rating: 4.8,
      playCount: 15420,
    },
    {
      title: 'Sprunka',
      image: null,
      cover_img_url: 'https://tinyfun.io/images/sprunka-sprunka-sprunka.webp',
      link: '/sprunka-sprunka-sprunka',
      description: 'Original Sprunka music creation game',
      rating: 4.7,
      playCount: 12350,
    },
    {
      title: 'TinyFun Merge',
      image: '/icon/favicon.ico',
      cover_img_url: 'https://tinyfun.io/images/tinyfun-merge-merge-fellas-brainrot-edition.webp',
      link: '/tinyfun-merge-merge-fellas-brainrot-edition',
      description: 'Merge Fellas: Brainrot Edition - takes everything',
      rating: 4.6,
      playCount: 8920,
    },
    {
      title: 'Test URI',
      image: null,
      cover_img_url: 'https://tinyfun.io/images/test-uri.webp',
      link: '/test-uri',
      description: 'Test game for development',
      rating: 4.5,
      playCount: 5670,
    },
    {
      title: 'Test 2',
      image: '/icon/favicon.ico',
      cover_img_url: 'https://tinyfun.io/images/test-2.webp',
      link: '/test-2',
      description: 'Another test game',
      rating: 4.4,
      playCount: 3240,
    },
    {
      title: 'Test 1',
      image: null,
      cover_img_url: 'https://tinyfun.io/images/test-1.webp',
      link: '/test-1',
      description: 'First test game',
      rating: 4.3,
      playCount: 2180,
    },
    {
      title: 'Popular Game 7',
      image: '/icon/favicon.ico',
      cover_img_url: '/icon/favicon.ico',
      link: '#test7',
      description: 'Popular action game with amazing graphics',
      rating: 4.9,
      playCount: 25000,
    },
    {
      title: 'Adventure Quest',
      image: null,
      cover_img_url: '/icon/favicon.ico',
      link: '#test8',
      description: 'Epic adventure awaits in this fantasy world',
      rating: 4.8,
      playCount: 18500,
    },
  ];

  // 临时使用测试数据来验证布局
  const testRecommendedGames = recommendedGames.length > 0 ? recommendedGames : forceTestGames;

  const handleFullScreen = async () => {
    const iframeElement = document.querySelector("iframe");
    if (!iframeElement) return;

    const isMobile = /iPhone|iPad|iPod|Android/i.test(navigator.userAgent);

    toggleFullscreen({
      element: iframeElement,
      isFullScreen: false,
      isMobile: isMobile,
      onEnter: () => {
        console.log("进入全屏");
      },
      onExit: () => {
        console.log("退出全屏");
      },
    });
  };

  const getAspectRatioClass = () => {
    switch (landingInfo?.aspectRatio) {
      case "4:3":
        return "md:pb-[75%] pb-[100%]"; // PC端 4:3，移动端 1:1
      case "1:1":
        return "pb-[100%]";
      case "3:4":
        return "md:pb-[75%] pb-[133.33%]"; // PC端 4:3，移动端 3:4
      case "16:9":
      default:
        // 默认都用4:3
        return "md:pb-[75%]";
    }
  };

  return (
    <div
      style={{
        ...styles,
        // 确保组件不受父容器 flex 布局影响
        width: '100%',
        minHeight: '800px', // 固定最小高度
      }}
      className={`relative ${className || ''} max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 my-8 sm:my-12 py-8 lg:py-12`}
    >
      {/* 三栏布局容器 */}
      <div
        style={{
          display: 'flex',
          padding: '1.5rem 1rem', // 适中的垂直padding
          position: 'relative',
          zIndex: 20,
          gap: '1.5rem',
          alignItems: 'flex-start',
          minHeight: '700px', // 固定内容区高度
          maxWidth: '1600px',
          width: '100%',
        }}
      >
        {/* 左侧边栏 - 推荐游戏 */}
        <div
          style={{
            display: window.innerWidth >= 1024 ? 'block' : 'none', // lg:block
            width: '11rem', // 调整宽度从15rem到11rem
            flexShrink: 0, // flex-shrink-0
            position: 'sticky',
            top: '2rem',
            maxHeight: 'calc(100vh - 4rem)',
            // overflowY: 'auto',
          }}
        >
            <div
              style={{
                backgroundColor: styles?.cardBackgroundColor,
                // backdropFilter: 'blur(12px)',
                borderRadius: '1rem',
                padding: '0rem', // 移除padding
                minHeight: '200px',
              }}
            >
              <div className="space-y-4">
                {testRecommendedGames.slice(0, 5).map((game, index) => (
                  <RecommendedGameCard
                    key={`left-${game.link}-${index}`}
                    game={game}
                    styles={{
                      cardBackgroundColor: 'rgba(0,0,0,0.15)',
                      cardBorderRadius: '0.75rem',
                      cardPadding: '0rem',
                      cardBackdropFilter: 'blur(8px)',
                      titleColor: styles?.descriptionColor || '#FFFFFF',
                      titleFontSize: '0.9rem',
                      titleFontWeight: '600',
                      // descriptionColor: styles?.descriptionColor || 'rgba(255, 255, 255, 0.8)',
                      // descriptionFontSize: '0.8rem',
                      buttonBackgroundColor: styles?.buttonBackgroundColor || '#FF9500',
                      buttonTextColor: styles?.buttonTextColor || '#FFFFFF',
                      buttonHoverBackgroundColor: styles?.buttonHoverBackgroundColor || '#E6850E',
                      buttonBorderRadius: '6px',
                    }}
                    compact={true}
                    className="w-full"
                  />
                ))}
              </div>
            </div>
          </div>

        {/* 中间主内容区域 */}
        <div style={{ flex: '1', minWidth: '0' }}>
          <section id="hero" className="relative">
            <div className="relative z-20">
        {/* 文本内容区域 - 标题和描述 */}
        <div className="text-center mb-6">
          <h1
            className="text-3xl md:text-4xl lg:text-5xl font-bold leading-tight bg-clip-text mb-4"
            style={{ color: styles?.h1Color || undefined }}
          >
            {mainTitle}
            {subTitle && (
              <>
                <br />
                <span
                  className="text-xl md:text-2xl font-semibold my-4"
                  style={{ color: styles?.h1Color || undefined }}
                  aria-label={subTitle.replace(/^[-\s]+/, '')}
                >
                  {subTitle.replace(/^[-\s]+/, '')}
                </span>
              </>
            )}
          </h1>
          <p
            className="hidden md:block text-lg md:text-xl mb-4 max-w-2xl mx-auto"
            style={{ color: styles?.descriptionColor || undefined }}
          >
            {description}
          </p>
        </div>
        
        {/* 游戏区域 - iframe居中展示 */}
        <div className="mx-auto max-w-5xl flex justify-center items-center" id="gameFrame">
          {iframeSource ? (
            <div
              className={`relative w-full rounded-lg overflow-hidden ${getAspectRatioClass()} h-[100vh] md:h-auto`}
              style={{ maxWidth: '900px', margin: '0 auto', minHeight: '560px' }}
            >
              <div className="absolute inset-0 w-full h-full">
                <iframe
                  src={iframeSource}
                  title={title}
                  className="w-full h-full border-0 rounded-lg"
                  style={{ minWidth: '320px', height: '100%' }}
                  allowFullScreen={allowFullScreen}
                  onLoad={() => setLoading(false)}
                />
              </div>
            </div>
          ) : (
            // Fallback game image if no iframe source
            <div className="relative rounded-lg overflow-hidden shadow-[0_8px_30px_rgba(0,0,0,0.4)] hover:shadow-[0_8px_30px_rgba(0,0,0,0.5)] transition-all transform hover:scale-[1.02]">
              <img 
                src="/icon/game-screenshot.jpg" 
                alt={title || "Game screenshot"} 
                className="w-full h-auto rounded-lg"
                onError={(e) => {
                  e.currentTarget.src = "/icon/favicon.ico";
                  e.currentTarget.className = "w-full h-auto rounded-lg object-contain bg-gray-800 p-4";
                }}
              />
              <div className="absolute inset-0 bg-gradient-to-t from-black/70 to-transparent flex items-end justify-center p-6">
                <button 
                  className="px-6 py-3 rounded-lg font-medium transition-all transform hover:scale-105"
                  style={{ 
                    backgroundColor: styles?.buttonBackgroundColor || "#4f46e5",
                    color: styles?.buttonTextColor || "#ffffff"
                  }}
                >
                  {buttonText}
                </button>
              </div>
            </div>
          )}
        </div>

        {/* 分享功能区域 */}
        <div className="flex items-center justify-center gap-4 mt-6">
          <FacebookShareButton url={typeof window !== 'undefined' ? window.location.href : ''}>
            <FacebookIcon size={36} round />
          </FacebookShareButton>
          <TwitterShareButton url={typeof window !== 'undefined' ? window.location.href : ''}>
            <TwitterIcon size={36} round />
          </TwitterShareButton>
          <RedditShareButton url={typeof window !== 'undefined' ? window.location.href : ''}>
            <RedditIcon size={36} round />
          </RedditShareButton>
          <WhatsappShareButton url={typeof window !== 'undefined' ? window.location.href : ''}>
            <WhatsappIcon size={36} round />
          </WhatsappShareButton>
          <TelegramShareButton url={typeof window !== 'undefined' ? window.location.href : ''}>
            <TelegramIcon size={36} round />
          </TelegramShareButton>
          <LineShareButton url={typeof window !== 'undefined' ? window.location.href : ''}>
            <LineIcon size={36} round />
          </LineShareButton>
        </div>

            </div>
          </section>
        </div>

        {/* 右侧边栏 - 更多推荐游戏 */}
        <div
          style={{
            display: window.innerWidth >= 1280 ? 'block' : 'none', // xl:block
            width: '11rem', // 调整宽度从15rem到11rem
            flexShrink: 0,
            position: 'sticky',
            top: '2rem',
            maxHeight: 'calc(100vh - 4rem)',
            overflowY: 'auto',
          }}
        >
            <div
              style={{
                backgroundColor: styles?.cardBackgroundColor,
                // backdropFilter: 'blur(12px)',
                borderRadius: '1rem',
                padding: '0rem', // 移除padding
                minHeight: '200px',
              }}
            >
              <div className="space-y-4">
                {testRecommendedGames.slice(5, 12).map((game, index) => (
                  <RecommendedGameCard
                    key={`right-${game.link}-${index}`}
                    game={game}
                    styles={{
                      // cardBackgroundColor: 'rgba(0,0,0,0.15)',
                      cardBorderRadius: '0.75rem',
                      cardPadding: '0rem',
                      cardBackdropFilter: 'blur(8px)',
                      titleColor: styles?.descriptionColor || '#FFFFFF',
                      titleFontSize: '0.9rem',
                      titleFontWeight: '600',
                      descriptionColor: styles?.descriptionColor || 'rgba(255, 255, 255, 0.8)',
                      descriptionFontSize: '0.8rem',
                      buttonBackgroundColor: styles?.buttonBackgroundColor || '#FF9500',
                      buttonTextColor: styles?.buttonTextColor || '#FFFFFF',
                      buttonHoverBackgroundColor: styles?.buttonHoverBackgroundColor || '#E6850E',
                      buttonBorderRadius: '6px',
                    }}
                    compact={true}
                    className="w-full"
                  />
                ))}
              </div>
            </div>
          </div>

      </div>

      {/* 移动端推荐游戏区域 - 在小屏幕上显示在底部 */}
      {showRecommendedGames && testRecommendedGames.length > 0 && (
        <div className="block lg:hidden mt-12 px-4 sm:px-6">
          <div className="text-center mb-8">
            <p
              className="text-base md:text-lg opacity-80"
              style={{ color: styles?.descriptionColor || 'rgba(255, 255, 255, 0.8)' }}
            >
              Discover more games to play
            </p>
          </div>

          {/* 移动端游戏网格 */}
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-6 max-w-2xl mx-auto">
            {testRecommendedGames.slice(0, 6).map((game, index) => (
              <div key={`mobile-${game.link}-${index}`} className="flex justify-center">
                <RecommendedGameCard
                  game={game}
                  styles={{
                    cardBackgroundColor: 'rgba(0,0,0,0.15)',
                    cardBorderRadius: '1rem',
                    cardPadding: '1.5rem',
                    cardBackdropFilter: 'blur(8px)',
                    titleColor: styles?.titleColor || '#FFFFFF',
                    titleFontSize: '1.1rem',
                    titleFontWeight: '600',
                    descriptionColor: styles?.descriptionColor || 'rgba(255, 255, 255, 0.8)',
                    descriptionFontSize: '0.9rem',
                    buttonBackgroundColor: styles?.buttonBackgroundColor || '#FF9500',
                    buttonTextColor: styles?.buttonTextColor || '#FFFFFF',
                    buttonHoverBackgroundColor: styles?.buttonHoverBackgroundColor || '#E6850E',
                    buttonBorderRadius: '8px',
                  }}
                  compact={false}
                  className="w-full max-w-sm"
                />
              </div>
            ))}
          </div>

          {/* 移动端查看更多按钮 */}
          {testRecommendedGames.length > 6 && (
            <div className="text-center mt-8">
              <button
                className="px-6 py-3 rounded-lg font-medium transition-all transform hover:scale-105"
                style={{
                  backgroundColor: styles?.buttonBackgroundColor || '#FF9500',
                  color: styles?.buttonTextColor || '#FFFFFF',
                }}
                onClick={() => {
                  console.log('Show more games on mobile');
                }}
              >
                View More Games ({testRecommendedGames.length - 6} more)
              </button>
            </div>
          )}
        </div>
      )}
    </div>
  );
}
