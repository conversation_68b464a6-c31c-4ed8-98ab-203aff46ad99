'use client';

import { RecommendedGame, LandingComponentStyles } from '../landing/types';
import RecommendedGameCard from './RecommendedGameCard';
import { useScreenSize, useLayoutConfig, getResponsiveClasses, getGameCounts } from './ResponsiveBreakpoints';

interface SmartResponsiveGameLayoutProps {
  games: RecommendedGame[];
  styles?: LandingComponentStyles;
  showRecommendedGames?: boolean;
  children: React.ReactNode;
}

/**
 * 智能响应式游戏布局组件
 * 
 * 特性：
 * - 基于精确断点的响应式布局
 * - SSR 安全的客户端渲染
 * - 遵循 SOLID 原则的组件设计
 * - 支持用户自定义样式
 */
export default function SmartResponsiveGameLayout({
  games,
  styles,
  showRecommendedGames = true,
  children
}: SmartResponsiveGameLayoutProps) {
  
  const screenSize = useScreenSize();
  const layoutConfig = useLayoutConfig();
  const responsiveClasses = getResponsiveClasses(screenSize);
  const gameCounts = getGameCounts(screenSize);

  // 如果不显示推荐游戏或没有游戏数据，使用简单布局
  if (!showRecommendedGames || !games || games.length === 0) {
    return (
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 my-8 sm:my-12 py-8 lg:py-12">
        {children}
      </div>
    );
  }

  // 游戏卡片样式配置 - 支持超紧凑模式
  const getGameCardStyles = (compact: boolean = true, ultraCompact: boolean = false) => ({
    cardBackgroundColor: 'rgba(0,0,0,0.15)',
    cardBorderRadius: ultraCompact ? '0.5rem' : '0.75rem',
    cardPadding: '0rem',
    cardBackdropFilter: 'blur(8px)',
    titleColor: styles?.descriptionColor || '#FFFFFF',
    titleFontSize: ultraCompact ? '0.75rem' : compact ? '0.85rem' : '1rem',
    titleFontWeight: '600',
    buttonBackgroundColor: styles?.buttonBackgroundColor || '#FF9500',
    buttonTextColor: styles?.buttonTextColor || '#FFFFFF',
    buttonHoverBackgroundColor: styles?.buttonHoverBackgroundColor || '#E6850E',
    buttonBorderRadius: ultraCompact ? '4px' : '6px',
  });

  // 侧边栏容器样式
  const sidebarContainerStyle = {
    backgroundColor: styles?.cardBackgroundColor,
    borderRadius: '1rem',
    padding: '0rem',
    minHeight: '200px',
  };

  return (
    <div
      style={{
        ...styles,
        width: '100%',
        minHeight: '800px',
      }}
      className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 my-8 sm:my-12 py-8 lg:py-12"
    >
      {/* 三栏响应式布局容器 */}
      <div className="flex flex-col lg:flex-row gap-6 lg:gap-8 relative z-20 py-6 lg:py-8 px-4 lg:px-6 max-w-[1600px] w-full mx-auto">
        
        {/* 左侧边栏 - 基于屏幕尺寸动态显示 */}
        {layoutConfig.leftSidebar.visible && (
          <aside className={responsiveClasses.leftSidebar}>
            <div style={sidebarContainerStyle}>
              <div className={responsiveClasses.leftGrid}>
                {games.slice(0, gameCounts.leftSidebar).map((game, index) => (
                  <RecommendedGameCard
                    key={`left-${game.link}-${index}`}
                    game={game}
                    styles={getGameCardStyles(true, true)}
                    compact={true}
                    ultraCompact={true}
                    className="w-full"
                  />
                ))}
              </div>
            </div>
          </aside>
        )}

        {/* 中间主内容区域 */}
        <main className="flex-1 min-w-0">
          {children}
        </main>

        {/* 右侧边栏 - 基于屏幕尺寸动态显示 */}
        {layoutConfig.rightSidebar.visible && (
          <aside className={responsiveClasses.rightSidebar}>
            <div style={sidebarContainerStyle}>
              <div className={responsiveClasses.rightGrid}>
                {games.slice(gameCounts.leftSidebar, gameCounts.leftSidebar + gameCounts.rightSidebar).map((game, index) => (
                  <RecommendedGameCard
                    key={`right-${game.link}-${index}`}
                    game={game}
                    styles={getGameCardStyles(true, true)}
                    compact={true}
                    ultraCompact={true}
                    className="w-full"
                  />
                ))}
              </div>
            </div>
          </aside>
        )}

      </div>

      {/* 移动端和平板端推荐游戏区域 */}
      {layoutConfig.bottomGrid.visible && (
        <section className="block lg:hidden mt-12 px-4 sm:px-6">
          <div className="text-center mb-8">
            <h2
              className="text-xl md:text-2xl font-semibold mb-2"
              style={{ color: styles?.titleColor || '#FFFFFF' }}
            >
              More Games to Play
            </h2>
            <p
              className="text-base md:text-lg opacity-80"
              style={{ color: styles?.descriptionColor || 'rgba(255, 255, 255, 0.8)' }}
            >
              Discover amazing games you&apos;ll love
            </p>
          </div>

          {/* 响应式游戏网格 */}
          <div className={responsiveClasses.bottomGrid}>
            {games.slice(0, gameCounts.bottomGrid).map((game, index) => (
              <div key={`mobile-${game.link}-${index}`} className="flex justify-center">
                <RecommendedGameCard
                  game={game}
                  styles={{
                    ...getGameCardStyles(false),
                    cardPadding: '1rem',
                    cardBorderRadius: '1rem',
                    descriptionColor: styles?.descriptionColor || 'rgba(255, 255, 255, 0.8)',
                    descriptionFontSize: '0.875rem',
                    buttonBorderRadius: '8px',
                  }}
                  compact={false}
                  className="w-full"
                />
              </div>
            ))}
          </div>

          {/* 查看更多按钮 */}
          {games.length > gameCounts.bottomGrid && (
            <div className="text-center mt-8">
              <button
                className="px-6 py-3 rounded-lg font-medium transition-all transform hover:scale-105 active:scale-95"
                style={{
                  backgroundColor: styles?.buttonBackgroundColor || '#FF9500',
                  color: styles?.buttonTextColor || '#FFFFFF',
                }}
                onClick={() => {
                  console.log('Show more games on mobile/tablet');
                }}
              >
                View More Games ({games.length - gameCounts.bottomGrid} more)
              </button>
            </div>
          )}
        </section>
      )}
    </div>
  );
}
