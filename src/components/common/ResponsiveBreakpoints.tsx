'use client';

import { useState, useEffect } from 'react';

/**
 * 响应式断点配置
 * 基于用户需求的精确断点定义
 */
export const BREAKPOINTS = {
  // 移动端
  mobile: 768,
  // 平板端
  tablet: 990,
  // 中屏幕
  medium: 1320,
  // 中大屏幕
  large: 1870,
  // 大屏幕
  xlarge: 1870
} as const;

/**
 * 屏幕尺寸类型
 */
export type ScreenSize = 'mobile' | 'tablet' | 'medium' | 'large' | 'xlarge';

/**
 * 获取当前屏幕尺寸类型
 */
export function getScreenSize(width: number): ScreenSize {
  if (width < BREAKPOINTS.mobile) return 'mobile';
  if (width < BREAKPOINTS.tablet) return 'tablet';
  if (width < BREAKPOINTS.medium) return 'medium';
  if (width < BREAKPOINTS.large) return 'large';
  return 'xlarge';
}

/**
 * 响应式布局配置
 */
export const LAYOUT_CONFIG = {
  mobile: {
    leftSidebar: { visible: false, columns: 0 },
    rightSidebar: { visible: false, columns: 0 },
    bottomGrid: { visible: true, columns: 2 }
  },
  tablet: {
    leftSidebar: { visible: false, columns: 0 },
    rightSidebar: { visible: false, columns: 0 },
    bottomGrid: { visible: true, columns: 4 }
  },
  medium: {
    leftSidebar: { visible: false, columns: 0 },
    rightSidebar: { visible: true, columns: 2 },
    bottomGrid: { visible: false, columns: 0 }
  },
  large: {
    leftSidebar: { visible: true, columns: 1 },
    rightSidebar: { visible: true, columns: 2 },
    bottomGrid: { visible: false, columns: 0 }
  },
  xlarge: {
    leftSidebar: { visible: true, columns: 2 },
    rightSidebar: { visible: true, columns: 2 },
    bottomGrid: { visible: false, columns: 0 }
  }
} as const;

/**
 * 自定义 Hook：使用窗口尺寸
 * 支持 SSR 安全的响应式检测
 */
export function useWindowSize() {
  const [windowSize, setWindowSize] = useState({
    width: 0,
    height: 0,
  });

  useEffect(() => {
    function handleResize() {
      setWindowSize({
        width: window.innerWidth,
        height: window.innerHeight,
      });
    }

    // 初始化时设置尺寸
    handleResize();

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  return windowSize;
}

/**
 * 自定义 Hook：获取当前屏幕尺寸类型
 */
export function useScreenSize(): ScreenSize {
  const { width } = useWindowSize();
  return getScreenSize(width);
}

/**
 * 自定义 Hook：获取当前布局配置
 */
export function useLayoutConfig() {
  const screenSize = useScreenSize();
  return LAYOUT_CONFIG[screenSize];
}

/**
 * 响应式类名生成器
 * 基于屏幕尺寸生成对应的 Tailwind CSS 类名
 */
export function getResponsiveClasses(screenSize: ScreenSize) {
  const config = LAYOUT_CONFIG[screenSize];
  
  return {
    leftSidebar: config.leftSidebar.visible
      ? `block w-40 xl:w-44 2xl:w-48 flex-shrink-0 sticky top-8 self-start max-h-[calc(100vh-4rem)] overflow-y-auto`
      : 'hidden',

    rightSidebar: config.rightSidebar.visible
      ? `block w-40 xl:w-44 2xl:w-48 flex-shrink-0 sticky top-8 self-start max-h-[calc(100vh-4rem)] overflow-y-auto`
      : 'hidden',

    leftGrid: config.leftSidebar.columns === 2
      ? 'grid grid-cols-1 2xl:grid-cols-2 gap-2'
      : 'grid grid-cols-1 gap-2',

    rightGrid: config.rightSidebar.columns === 2
      ? 'grid grid-cols-2 gap-2'
      : 'grid grid-cols-1 gap-2',

    bottomGrid: config.bottomGrid.visible
      ? `grid grid-cols-${config.bottomGrid.columns} gap-4 md:gap-6 max-w-6xl mx-auto`
      : 'hidden'
  };
}

/**
 * 响应式游戏数量计算
 * 基于屏幕尺寸和列数优化游戏显示数量
 */
export function getGameCounts(screenSize: ScreenSize) {
  const config = LAYOUT_CONFIG[screenSize];

  // 根据列数计算合适的游戏数量
  const leftCount = config.leftSidebar.visible
    ? (config.leftSidebar.columns === 2 ? 6 : 4) // 2列显示6个，1列显示4个
    : 0;

  const rightCount = config.rightSidebar.visible
    ? (config.rightSidebar.columns === 2 ? 8 : 4) // 2列显示8个，1列显示4个
    : 0;

  return {
    leftSidebar: leftCount,
    rightSidebar: rightCount,
    bottomGrid: config.bottomGrid.visible ? 8 : 0
  };
}
