'use client';

import { RecommendedGame, LandingComponentStyles } from '../landing/types';
import RecommendedGameCard from './RecommendedGameCard';

interface ResponsiveGameLayoutProps {
  games: RecommendedGame[];
  styles?: LandingComponentStyles;
  showRecommendedGames?: boolean;
  children: React.ReactNode; // 主内容区域
}

/**
 * 响应式游戏布局组件 - 遵循 SOLID 原则
 *
 * 布局规则（基于 Tailwind CSS 断点）：
 * - 2xl (≥1536px): 左侧2列 + 中间内容 + 右侧2列
 * - xl (1280px-1535px): 左侧1列 + 中间内容 + 右侧2列
 * - lg (1024px-1279px): 隐藏左侧 + 中间内容 + 右侧2列
 * - md (768px-1023px): 隐藏侧边栏 + 中间内容 + 底部4列网格
 * - sm (<768px): 隐藏侧边栏 + 中间内容 + 底部2列网格
 *
 * SOLID 原则应用：
 * - SRP: 单一职责 - 仅负责响应式布局管理
 * - OCP: 开放封闭 - 通过 props 扩展功能
 * - DIP: 依赖倒置 - 依赖抽象的样式接口
 */
export default function ResponsiveGameLayout({
  games,
  styles,
  showRecommendedGames = true,
  children
}: ResponsiveGameLayoutProps) {
  
  if (!showRecommendedGames || !games || games.length === 0) {
    return (
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 my-8 sm:my-12 py-8 lg:py-12">
        {children}
      </div>
    );
  }

  // 游戏卡片样式配置
  const getGameCardStyles = (compact: boolean = true) => ({
    cardBackgroundColor: 'rgba(0,0,0,0.15)',
    cardBorderRadius: '0.75rem',
    cardPadding: '0rem',
    cardBackdropFilter: 'blur(8px)',
    titleColor: styles?.descriptionColor || '#FFFFFF',
    titleFontSize: compact ? '0.85rem' : '1rem',
    titleFontWeight: '600',
    buttonBackgroundColor: styles?.buttonBackgroundColor || '#FF9500',
    buttonTextColor: styles?.buttonTextColor || '#FFFFFF',
    buttonHoverBackgroundColor: styles?.buttonHoverBackgroundColor || '#E6850E',
    buttonBorderRadius: '6px',
  });

  // 侧边栏容器样式
  const sidebarContainerStyle = {
    backgroundColor: styles?.cardBackgroundColor,
    borderRadius: '1rem',
    padding: '0rem',
    minHeight: '200px',
  };

  return (
    <div
      style={{
        ...styles,
        width: '100%',
        minHeight: '800px',
      }}
      className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 my-8 sm:my-12 py-8 lg:py-12"
    >
      {/* 三栏响应式布局容器 */}
      <div className="flex flex-col lg:flex-row gap-6 lg:gap-8 relative z-20 py-6 lg:py-8 px-4 lg:px-6 max-w-[1600px] w-full mx-auto">
        
        {/* 左侧边栏 - 响应式显示和列数 */}
        <aside className="hidden lg:block w-44 xl:w-48 2xl:w-52 flex-shrink-0 sticky top-8 self-start max-h-[calc(100vh-4rem)] overflow-y-auto">
          <div style={sidebarContainerStyle}>
            {/* 响应式网格：lg-xl时1列，2xl时2列 */}
            <div className="grid grid-cols-1 2xl:grid-cols-2 gap-3">
              {games.slice(0, 8).map((game, index) => (
                <RecommendedGameCard
                  key={`left-${game.link}-${index}`}
                  game={game}
                  styles={getGameCardStyles(true)}
                  compact={true}
                  className="w-full"
                />
              ))}
            </div>
          </div>
        </aside>

        {/* 中间主内容区域 */}
        <main className="flex-1 min-w-0">
          {children}
        </main>

        {/* 右侧边栏 - 中大屏幕显示，固定2列 */}
        <aside className="hidden xl:block w-44 2xl:w-52 flex-shrink-0 sticky top-8 self-start max-h-[calc(100vh-4rem)] overflow-y-auto">
          <div style={sidebarContainerStyle}>
            {/* 固定2列网格布局 */}
            <div className="grid grid-cols-2 gap-3">
              {games.slice(8, 20).map((game, index) => (
                <RecommendedGameCard
                  key={`right-${game.link}-${index}`}
                  game={game}
                  styles={getGameCardStyles(true)}
                  compact={true}
                  className="w-full"
                />
              ))}
            </div>
          </div>
        </aside>

      </div>

      {/* 移动端和平板端推荐游戏区域 */}
      <section className="block lg:hidden mt-12 px-4 sm:px-6">
        <div className="text-center mb-8">
          <h2
            className="text-xl md:text-2xl font-semibold mb-2"
            style={{ color: styles?.titleColor || '#FFFFFF' }}
          >
            More Games to Play
          </h2>
          <p
            className="text-base md:text-lg opacity-80"
            style={{ color: styles?.descriptionColor || 'rgba(255, 255, 255, 0.8)' }}
          >
            Discover amazing games you&apos;ll love
          </p>
        </div>

        {/* 响应式游戏网格 - 移动端2列，平板端4列 */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 md:gap-6 max-w-6xl mx-auto">
          {games.slice(0, 8).map((game, index) => (
            <div key={`mobile-${game.link}-${index}`} className="flex justify-center">
              <RecommendedGameCard
                game={game}
                styles={{
                  ...getGameCardStyles(false),
                  cardPadding: '1rem',
                  cardBorderRadius: '1rem',
                  descriptionColor: styles?.descriptionColor || 'rgba(255, 255, 255, 0.8)',
                  descriptionFontSize: '0.875rem',
                  buttonBorderRadius: '8px',
                }}
                compact={false}
                className="w-full"
              />
            </div>
          ))}
        </div>

        {/* 查看更多按钮 */}
        {games.length > 8 && (
          <div className="text-center mt-8">
            <button
              className="px-6 py-3 rounded-lg font-medium transition-all transform hover:scale-105 active:scale-95"
              style={{
                backgroundColor: styles?.buttonBackgroundColor || '#FF9500',
                color: styles?.buttonTextColor || '#FFFFFF',
              }}
              onClick={() => {
                console.log('Show more games on mobile/tablet');
              }}
            >
              View More Games ({games.length - 8} more)
            </button>
          </div>
        )}
      </section>
    </div>
  );
}
