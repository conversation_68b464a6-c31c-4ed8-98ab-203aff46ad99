'use client';

import Link from 'next/link';
import { RecommendedGame, RecommendedGameStyles } from '../landing/types';

interface RecommendedGameCardProps {
  game: RecommendedGame;
  styles?: RecommendedGameStyles;
  className?: string;
  compact?: boolean; // 紧凑模式，用于侧边栏
  ultraCompact?: boolean; // 超紧凑模式，用于大屏幕侧边栏
}

export default function RecommendedGameCard({
  game,
  styles,
  className = '',
  compact = false,
  ultraCompact = false
}: RecommendedGameCardProps) {
  if (!game) return null;

  // 动态计算 padding 基于紧凑模式
  const getPadding = () => {
    if (ultraCompact) return '0.25rem';
    if (compact) return '0.5rem';
    return '1.5rem';
  };

  // 默认样式
  const defaultCardStyle: React.CSSProperties = {
    backgroundColor: styles?.cardBackgroundColor || 'rgba(0,0,0,0.15)',
    borderRadius: styles?.cardBorderRadius || (ultraCompact ? '0.5rem' : '1rem'),
    padding: styles?.cardPadding || getPadding(),
    margin: styles?.cardMargin || (ultraCompact ? '0 0 0.25rem 0' : '0 0 0.5rem 0'),
    backdropFilter: styles?.cardBackdropFilter || 'blur(8px)',
    boxShadow: styles?.cardBoxShadow || 'none',
    border: styles?.cardBorderColor ? `1px solid ${styles.cardBorderColor}` : 'none',
    transition: 'transform 0.3s ease, box-shadow 0.3s ease',
    cursor: 'pointer',
  };

  // 动态计算字体大小
  const getTitleFontSize = () => {
    if (ultraCompact) return '0.75rem';
    if (compact) return '0.85rem';
    return '1.1rem';
  };

  const titleStyle: React.CSSProperties = {
    color: styles?.titleColor || '#FFFFFF',
    fontSize: styles?.titleFontSize || getTitleFontSize(),
    fontWeight: styles?.titleFontWeight || '600',
    margin: ultraCompact ? '0 0 0.25rem 0' : '0 0 0.5rem 0',
    overflow: 'hidden',
    textOverflow: 'ellipsis',
    whiteSpace: 'nowrap',
  };

  const descriptionStyle: React.CSSProperties = {
    color: styles?.descriptionColor || 'rgba(255, 255, 255, 0.8)',
    fontSize: styles?.descriptionFontSize || (compact ? '0.8rem' : '0.9rem'),
    lineHeight: '1.4',
    overflow: 'hidden',
    textOverflow: 'ellipsis',
    display: '-webkit-box',
    WebkitLineClamp: compact ? 2 : 3,
    WebkitBoxOrient: 'vertical',
    margin: '0 0 1rem 0',
  };

  // 动态计算按钮样式
  const getButtonPadding = () => {
    if (ultraCompact) return '4px 8px';
    if (compact) return '6px 12px';
    return '8px 16px';
  };

  const getButtonFontSize = () => {
    if (ultraCompact) return '0.7rem';
    if (compact) return '0.75rem';
    return '0.9rem';
  };

  const buttonStyle: React.CSSProperties = {
    backgroundColor: styles?.buttonBackgroundColor || '#FF9500',
    color: styles?.buttonTextColor || '#FFFFFF',
    borderRadius: styles?.buttonBorderRadius || (ultraCompact ? '4px' : '8px'),
    padding: styles?.buttonPadding || getButtonPadding(),
    fontSize: styles?.buttonFontSize || getButtonFontSize(),
    fontWeight: styles?.buttonFontWeight || '500',
    border: 'none',
    textDecoration: 'none',
    display: 'inline-block',
    textAlign: 'center' as const,
    transition: 'background-color 0.3s ease',
    width: '100%',
  };

  const imageStyle: React.CSSProperties = {
    backgroundColor: styles?.imageBackgroundColor || '#f3f4f6',
    borderRadius: styles?.imageBorderRadius || '8px',
    overflow: 'hidden',
  };

  return (
    <div
      className={`recommended-game-card rounded-xl overflow-hidden transition-transform duration-300 hover:scale-105 group ${className}`}
      style={{
        ...defaultCardStyle,
        display: 'flex',
        flexDirection: 'column',
        cursor: 'pointer',
      }}
    >
      {/* 游戏封面图片 - 动态宽高比 */}
      <div
        className={`relative overflow-hidden ${
          ultraCompact ? 'aspect-[4/3]' : compact ? 'aspect-video' : 'aspect-square'
        }`}
        style={imageStyle}
      >
        <img
          src={(game.cover_img_url && game.cover_img_url !== '') ? game.cover_img_url : (game.image && game.image !== '') ? game.image : '/icon/favicon.ico'}
          alt={game.title}
          className="w-full h-full object-cover transition-transform duration-700 group-hover:scale-110"
          onError={(e) => {
            e.currentTarget.src = "/icon/favicon.ico";
            e.currentTarget.className = "w-full h-full object-contain bg-gray-800 p-4";
          }}
        />
        {/* Hover 覆盖层 - 参考 ExplorationGameList */}
        <div className="absolute inset-0 bg-gradient-to-t from-black/80 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-end">
          <Link
            href={game.link}
            className="w-full text-center py-2 mb-2 mx-2 rounded-lg font-medium transition-all"
            style={{
              ...buttonStyle,
              fontSize: ultraCompact ? '0.65rem' : compact ? '0.7rem' : '0.875rem',
            }}
          >
            {ultraCompact ? 'Play' : compact ? 'Play' : 'Play Now'}
          </Link>
        </div>
      </div>

      {/* 游戏信息 - 参考 ExplorationGameList 设计 */}
      <div className={`flex flex-col ${
        ultraCompact ? 'p-1' : compact ? 'p-2' : 'p-3'
      }`}>
        <p
          className={`font-semibold truncate ${
            ultraCompact ? 'text-xs' : compact ? 'text-sm' : 'text-base'
          }`}
          style={titleStyle}
          title={game.title}
        >
          {game.title}
        </p>

        {!compact && game.description && (
          <p
            className="text-xs mt-1 line-clamp-2"
            style={{
              ...descriptionStyle,
              display: '-webkit-box',
              WebkitLineClamp: 2,
              WebkitBoxOrient: 'vertical',
              overflow: 'hidden',
              margin: '0.25rem 0 0 0',
            }}
          >
            {game.description}
          </p>
        )}

        {/* 游戏统计信息（可选） - 仅在非紧凑模式下显示 */}
        {!compact && (game.rating || game.playCount) && (
          <div
            style={{
              display: 'flex',
              justifyContent: 'space-between',
              alignItems: 'center',
              marginTop: '0.5rem',
              fontSize: '0.75rem',
              color: 'rgba(255, 255, 255, 0.6)',
            }}
          >
            {game.rating && (
              <span>⭐ {game.rating.toFixed(1)}</span>
            )}
            {game.playCount && (
              <span>🎮 {game.playCount.toLocaleString()}</span>
            )}
          </div>
        )}
      </div>
    </div>
  );
}
