'use client';

import { useState } from 'react';
import { RecommendedGame, RecommendedGameStyles } from '../landing/types';
import RecommendedGameCard from './RecommendedGameCard';

interface RecommendedGameSidebarProps {
  games: RecommendedGame[];
  styles?: RecommendedGameStyles;
  className?: string;
  title?: string;
  maxInitialGames?: number; // 初始显示的游戏数量
  showLoadMore?: boolean; // 是否显示"Load More"按钮
  gridColumns?: 1 | 2; // 网格列数：1列或2列
  layout?: 'vertical' | 'grid'; // 布局模式：垂直列表或网格
}

export default function RecommendedGameSidebar({
  games,
  styles,
  className = '',
  title = 'Recommended Games',
  maxInitialGames = 6,
  showLoadMore = false,
  gridColumns = 1,
  layout = 'vertical'
}: RecommendedGameSidebarProps) {
  const [visibleGames, setVisibleGames] = useState(maxInitialGames);

  if (!games || games.length === 0) return null;

  // 默认侧边栏样式
  const sidebarStyle: React.CSSProperties = {
    backgroundColor: styles?.sidebarBackgroundColor || 'rgba(0,0,0,0.18)',
    borderRadius: styles?.sidebarBorderRadius || '1rem',
    padding: styles?.sidebarPadding || '1.5rem',
    margin: styles?.sidebarMargin || '0',
    backdropFilter: styles?.sidebarBackdropFilter || 'blur(12px)',
    boxShadow: styles?.sidebarBoxShadow || 'none',
    position: 'sticky' as const,
    top: '2rem',
    maxHeight: 'calc(100vh - 4rem)',
    overflowY: 'auto' as const,
  };

  const titleStyle: React.CSSProperties = {
    color: styles?.sidebarTitleColor || '#FFFFFF',
    fontSize: styles?.sidebarTitleFontSize || '1.5rem',
    fontWeight: styles?.sidebarTitleFontWeight || '700',
    margin: '0 0 1.5rem 0',
    textAlign: 'center' as const,
  };

  const loadMoreButtonStyle: React.CSSProperties = {
    backgroundColor: styles?.buttonBackgroundColor || '#FF9500',
    color: styles?.buttonTextColor || '#FFFFFF',
    borderRadius: styles?.buttonBorderRadius || '8px',
    padding: '10px 20px',
    fontSize: '0.9rem',
    fontWeight: '500',
    border: 'none',
    cursor: 'pointer',
    width: '100%',
    marginTop: '1rem',
    transition: 'background-color 0.3s ease',
  };

  const handleLoadMore = () => {
    setVisibleGames(prev => Math.min(prev + maxInitialGames, games.length));
  };

  const displayedGames = games.slice(0, visibleGames);
  const hasMoreGames = visibleGames < games.length;

  return (
    <div className={`recommended-game-sidebar ${className}`} style={sidebarStyle}>
      {/* 侧边栏标题 */}
      <h2 style={titleStyle}>
        {title}
      </h2>

      {/* 游戏列表 - 支持响应式网格布局 */}
      <div
        className={`games-list ${layout === 'grid' ? `grid gap-3 ${gridColumns === 2 ? 'grid-cols-2' : 'grid-cols-1'}` : 'space-y-3'}`}
      >
        {displayedGames.map((game, index) => (
          <RecommendedGameCard
            key={`${game.link}-${index}`}
            game={game}
            styles={styles}
            compact={true}
            className="sidebar-game-card"
          />
        ))}
      </div>

      {/* Load More 按钮 */}
      {showLoadMore && hasMoreGames && (
        <button
          style={loadMoreButtonStyle}
          onClick={handleLoadMore}
          onMouseEnter={(e) => {
            e.currentTarget.style.backgroundColor = styles?.buttonHoverBackgroundColor || '#E6850E';
          }}
          onMouseLeave={(e) => {
            e.currentTarget.style.backgroundColor = styles?.buttonBackgroundColor || '#FF9500';
          }}
        >
          Load More ({games.length - visibleGames} remaining)
        </button>
      )}

      {/* 游戏统计信息 */}
      <div 
        style={{
          marginTop: '1.5rem',
          padding: '1rem',
          backgroundColor: 'rgba(255, 255, 255, 0.05)',
          borderRadius: '8px',
          textAlign: 'center' as const,
        }}
      >
        <p 
          style={{
            color: 'rgba(255, 255, 255, 0.7)',
            fontSize: '0.8rem',
            margin: '0',
          }}
        >
          Showing {displayedGames.length} of {games.length} games
        </p>
      </div>
    </div>
  );
}
